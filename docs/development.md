# Development Guide

## Getting Started

### Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js 18+**: [Download Node.js](https://nodejs.org/)
- **npm 9+**: Comes with Node.js
- **Docker**: [Download Docker](https://www.docker.com/get-started)
- **Docker Compose**: Usually included with Docker Desktop
- **Git**: [Download Git](https://git-scm.com/)

### Initial Setup

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd ecommerce-microservices
   ```

2. **Run the setup script**:
   ```bash
   ./scripts/setup-local.sh
   ```

3. **Start development**:
   ```bash
   npm run dev
   ```

## Project Structure

```
ecommerce-microservices/
├── apps/                    # Applications
│   ├── web/                # React frontend
│   ├── api-gateway/        # API Gateway
│   ├── user-service/       # User management
│   ├── product-service/    # Product catalog
│   └── order-service/      # Order management
├── packages/               # Shared packages
│   ├── shared-types/       # TypeScript types
│   ├── ui-components/      # React components
│   └── common-utils/       # Utilities
├── infrastructure/         # Infrastructure as Code
├── scripts/               # Build and deployment scripts
└── docs/                  # Documentation
```

## Development Workflow

### 1. Working with Workspaces

This project uses npm workspaces for managing multiple packages:

```bash
# Install dependencies for all workspaces
npm install

# Run a command in a specific workspace
npm run dev --workspace=apps/web
npm run build --workspace=packages/shared-types

# Run a command in all workspaces
npm run test
npm run lint
```

### 2. Development Commands

```bash
# Start all services in development mode
npm run dev

# Build all applications
npm run build

# Run tests
npm run test

# Run tests with coverage
npm run test:coverage

# Lint code
npm run lint

# Type check
npm run type-check

# Format code
npm run format
```

### 3. Working with Individual Services

Each service can be developed independently:

```bash
# Frontend development
cd apps/web
npm run dev

# API Gateway development
cd apps/api-gateway
npm run dev

# Service development
cd apps/user-service
npm run dev
```

## Code Standards

### TypeScript Configuration

- **Strict mode enabled**: All TypeScript strict checks are enabled
- **Path mapping**: Use absolute imports with path aliases
- **Shared types**: Use types from `@shared/types`

Example:
```typescript
import { User } from '@shared/types'
import { logger } from '@shared/utils'
import { Button } from '@shared/ui'
```

### Code Style

- **ESLint**: Enforces code style and best practices
- **Prettier**: Automatic code formatting
- **Husky**: Git hooks for pre-commit checks

### Naming Conventions

- **Files**: kebab-case (`user-service.ts`)
- **Directories**: kebab-case (`user-service/`)
- **Components**: PascalCase (`UserProfile.tsx`)
- **Functions**: camelCase (`getUserById`)
- **Constants**: UPPER_SNAKE_CASE (`API_BASE_URL`)

## Testing Strategy

### Unit Tests

- **Framework**: Vitest
- **Location**: `*.test.ts` files next to source code
- **Coverage**: Aim for >80% coverage

```bash
# Run unit tests
npm run test

# Run tests in watch mode
npm run test -- --watch

# Run tests with coverage
npm run test:coverage
```

### Integration Tests

- **Framework**: Vitest with Supertest
- **Location**: `tests/integration/` directory
- **Database**: Test database with Docker

```bash
# Run integration tests
npm run test:integration
```

### End-to-End Tests

- **Framework**: Playwright
- **Location**: `tests/e2e/` directory
- **Environment**: Full application stack

```bash
# Run E2E tests
npm run test:e2e
```

## Database Development

### Local Database

The development environment uses PostgreSQL and Redis running in Docker:

```bash
# Start databases
docker-compose up -d postgres redis

# Stop databases
docker-compose down
```

### Migrations

Database migrations are handled by each service:

```bash
# Run migrations for user service
cd apps/user-service
npm run migrate

# Create new migration
npm run migrate:create add_user_preferences
```

### Database Schema

Each service manages its own database schema:

- **User Service**: Users, profiles, authentication
- **Product Service**: Products, categories, inventory
- **Order Service**: Orders, order items, payments

## API Development

### REST API Guidelines

- **HTTP Methods**: Use appropriate HTTP methods (GET, POST, PUT, DELETE)
- **Status Codes**: Use standard HTTP status codes
- **Request/Response**: JSON format with consistent structure
- **Validation**: Use Zod schemas for request validation

Example API response:
```json
{
  "success": true,
  "data": {
    "id": "123",
    "name": "Product Name"
  },
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "totalPages": 10
  }
}
```

### Error Handling

- **Consistent Format**: All errors follow the same structure
- **Error Codes**: Use meaningful error codes
- **Logging**: Log all errors with context

Example error response:
```json
{
  "success": false,
  "error": "User not found",
  "code": "USER_NOT_FOUND"
}
```

## Frontend Development

### React Best Practices

- **Functional Components**: Use function components with hooks
- **TypeScript**: Strict typing for all components
- **State Management**: Zustand for global state
- **Data Fetching**: React Query for server state

### Component Structure

```typescript
// UserProfile.tsx
import { FC } from 'react'
import { User } from '@shared/types'

interface UserProfileProps {
  user: User
  onUpdate: (user: User) => void
}

export const UserProfile: FC<UserProfileProps> = ({ user, onUpdate }) => {
  // Component implementation
}
```

### Styling

- **Tailwind CSS**: Utility-first CSS framework
- **CSS Modules**: For component-specific styles
- **Design System**: Shared UI components

## Environment Configuration

### Environment Variables

Each service uses environment variables for configuration:

```bash
# .env file
DATABASE_URL=postgresql://postgres:password@localhost:5432/ecommerce
REDIS_URL=redis://localhost:6379
JWT_SECRET=your-secret-key
```

### Configuration Management

- **Development**: `.env` file
- **Production**: Kubernetes secrets and config maps
- **Validation**: Environment variables are validated at startup

## Debugging

### Local Debugging

- **VS Code**: Debug configuration included
- **Chrome DevTools**: For frontend debugging
- **Node.js Inspector**: For backend debugging

### Logging

- **Structured Logging**: JSON format with correlation IDs
- **Log Levels**: DEBUG, INFO, WARN, ERROR
- **Centralized**: All logs aggregated in development

### Monitoring

- **Health Checks**: Each service exposes health endpoints
- **Metrics**: Prometheus metrics for monitoring
- **Tracing**: Request tracing across services

## Performance Optimization

### Frontend Performance

- **Code Splitting**: Lazy loading of routes and components
- **Bundle Analysis**: Regular bundle size monitoring
- **Caching**: Aggressive caching strategies

### Backend Performance

- **Database Optimization**: Proper indexing and query optimization
- **Caching**: Redis for frequently accessed data
- **Connection Pooling**: Efficient database connections

## Security Considerations

### Authentication

- **JWT Tokens**: Stateless authentication
- **Refresh Tokens**: Secure token renewal
- **Password Hashing**: bcrypt for password storage

### Input Validation

- **Schema Validation**: Zod schemas for all inputs
- **Sanitization**: Clean user inputs
- **Rate Limiting**: Prevent abuse

### HTTPS

- **TLS Termination**: At load balancer level
- **Secure Headers**: Security headers for all responses
- **CORS**: Proper CORS configuration

## Troubleshooting

### Common Issues

1. **Port Conflicts**: Check if ports are already in use
2. **Database Connection**: Ensure PostgreSQL is running
3. **Node Modules**: Clear and reinstall if issues persist
4. **Docker Issues**: Restart Docker if containers fail

### Getting Help

- **Documentation**: Check this guide and architecture docs
- **Logs**: Check service logs for error details
- **Health Checks**: Verify service health endpoints
- **Team**: Reach out to team members for assistance
