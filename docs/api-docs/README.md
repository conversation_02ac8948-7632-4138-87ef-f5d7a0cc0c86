# API Documentation

## Overview

This directory contains the API documentation for all microservices in the e-commerce platform.

## Services

### API Gateway
- **Base URL**: `http://localhost:3000`
- **Documentation**: [API Gateway Docs](./api-gateway.md)

### User Service
- **Base URL**: `http://localhost:3001`
- **Documentation**: [User Service Docs](./user-service.md)

### Product Service
- **Base URL**: `http://localhost:3002`
- **Documentation**: [Product Service Docs](./product-service.md)

### Order Service
- **Base URL**: `http://localhost:3003`
- **Documentation**: [Order Service Docs](./order-service.md)

## Authentication

All protected endpoints require a JWT token in the Authorization header:

```
Authorization: Bearer <jwt-token>
```

## Response Format

All API responses follow a consistent format:

### Success Response
```json
{
  "success": true,
  "data": {
    // Response data
  },
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "totalPages": 10
  }
}
```

### Error Response
```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE"
}
```

## Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict
- `422` - Validation Error
- `500` - Internal Server Error

## Rate Limiting

API requests are rate limited:
- **Limit**: 100 requests per 15 minutes per IP
- **Headers**: Rate limit information is included in response headers

## Postman Collection

Import the Postman collection for easy API testing:
- [Download Collection](./postman/ecommerce-api.postman_collection.json)
