# Deployment Guide

## Overview

This guide covers deploying the E-commerce Microservices platform to different environments using Docker, Kubernetes, and various deployment strategies.

## Prerequisites

### Required Tools

- **Docker**: For containerization
- **Kubernetes**: For orchestration (kubectl configured)
- **Helm**: For package management (optional)
- **Terraform**: For infrastructure provisioning (optional)

### Environment Setup

Ensure you have access to:
- Container registry (Docker Hub, AWS ECR, etc.)
- Kubernetes cluster (EKS, GKE, AKS, or local)
- Database instances (PostgreSQL, Redis)

## Local Development Deployment

### Using Docker Compose

1. **Start all services**:
   ```bash
   docker-compose up -d
   ```

2. **Check service status**:
   ```bash
   docker-compose ps
   ```

3. **View logs**:
   ```bash
   docker-compose logs -f [service-name]
   ```

4. **Stop services**:
   ```bash
   docker-compose down
   ```

### Using Local Scripts

1. **Setup local environment**:
   ```bash
   ./scripts/setup-local.sh
   ```

2. **Start development servers**:
   ```bash
   npm run dev
   ```

## Production Deployment

### Building and Pushing Images

1. **Build all images**:
   ```bash
   ./scripts/build.sh
   ```

2. **Push to registry**:
   ```bash
   PUSH_TO_REGISTRY=true ./scripts/build.sh
   ```

### Kubernetes Deployment

#### Development Environment

1. **Deploy to development**:
   ```bash
   ./scripts/deploy.sh dev
   ```

2. **Verify deployment**:
   ```bash
   kubectl get pods -n ecommerce-dev
   kubectl get services -n ecommerce-dev
   ```

#### Staging Environment

1. **Deploy to staging**:
   ```bash
   ./scripts/deploy.sh staging
   ```

2. **Run smoke tests**:
   ```bash
   # Add your smoke test commands here
   ```

#### Production Environment

1. **Deploy to production**:
   ```bash
   ./scripts/deploy.sh prod
   ```

2. **Monitor deployment**:
   ```bash
   kubectl rollout status deployment/api-gateway -n ecommerce-prod
   ```

## Environment Configuration

### Environment Variables

Each environment requires specific configuration:

#### Development
```yaml
NODE_ENV: development
LOG_LEVEL: debug
DATABASE_URL: ********************************************/ecommerce_dev
REDIS_URL: redis://redis:6379/0
```

#### Staging
```yaml
NODE_ENV: staging
LOG_LEVEL: info
DATABASE_URL: **************************************/ecommerce_staging
REDIS_URL: redis://staging-redis:6379/0
```

#### Production
```yaml
NODE_ENV: production
LOG_LEVEL: warn
DATABASE_URL: ***********************************/ecommerce_prod
REDIS_URL: redis://prod-redis:6379/0
```

### Secrets Management

#### Kubernetes Secrets

Create secrets for sensitive data:

```bash
# Database credentials
kubectl create secret generic database-secret \
  --from-literal=url="********************************/db" \
  -n ecommerce-prod

# JWT secret
kubectl create secret generic jwt-secret \
  --from-literal=secret="your-jwt-secret" \
  -n ecommerce-prod

# Stripe keys
kubectl create secret generic stripe-secret \
  --from-literal=secret-key="sk_live_..." \
  --from-literal=publishable-key="pk_live_..." \
  -n ecommerce-prod
```

## Database Setup

### PostgreSQL

1. **Create database**:
   ```sql
   CREATE DATABASE ecommerce_prod;
   CREATE USER ecommerce_user WITH PASSWORD 'secure_password';
   GRANT ALL PRIVILEGES ON DATABASE ecommerce_prod TO ecommerce_user;
   ```

2. **Run migrations**:
   ```bash
   # Run migrations for each service
   kubectl exec -it deployment/user-service -n ecommerce-prod -- npm run migrate
   kubectl exec -it deployment/product-service -n ecommerce-prod -- npm run migrate
   kubectl exec -it deployment/order-service -n ecommerce-prod -- npm run migrate
   ```

### Redis

Configure Redis for caching and sessions:

```bash
# Connect to Redis
kubectl exec -it deployment/redis -n ecommerce-prod -- redis-cli

# Test connection
127.0.0.1:6379> ping
PONG
```

## Monitoring and Logging

### Health Checks

All services expose health endpoints:

```bash
# Check service health
curl http://api-gateway-service:3000/health
curl http://user-service:3001/health
curl http://product-service:3002/health
curl http://order-service:3003/health
```

### Prometheus Metrics

Services expose metrics at `/metrics` endpoint:

```bash
curl http://api-gateway-service:3000/metrics
```

### Log Aggregation

Logs are collected and can be viewed:

```bash
# View logs
kubectl logs -f deployment/api-gateway -n ecommerce-prod

# View logs from all pods
kubectl logs -f -l app=api-gateway -n ecommerce-prod
```

## Scaling

### Horizontal Pod Autoscaling

Configure HPA for automatic scaling:

```yaml
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: api-gateway-hpa
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: api-gateway
  minReplicas: 2
  maxReplicas: 10
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
```

### Manual Scaling

Scale deployments manually:

```bash
# Scale API Gateway
kubectl scale deployment api-gateway --replicas=5 -n ecommerce-prod

# Scale all services
kubectl scale deployment api-gateway user-service product-service order-service --replicas=3 -n ecommerce-prod
```

## Backup and Recovery

### Database Backup

1. **Create backup**:
   ```bash
   kubectl exec -it postgres-pod -- pg_dump -U postgres ecommerce_prod > backup.sql
   ```

2. **Restore backup**:
   ```bash
   kubectl exec -i postgres-pod -- psql -U postgres ecommerce_prod < backup.sql
   ```

### Configuration Backup

Backup Kubernetes configurations:

```bash
# Export all resources
kubectl get all -n ecommerce-prod -o yaml > ecommerce-backup.yaml

# Export secrets (be careful with sensitive data)
kubectl get secrets -n ecommerce-prod -o yaml > secrets-backup.yaml
```

## Rollback Procedures

### Rolling Back Deployments

1. **Check rollout history**:
   ```bash
   kubectl rollout history deployment/api-gateway -n ecommerce-prod
   ```

2. **Rollback to previous version**:
   ```bash
   kubectl rollout undo deployment/api-gateway -n ecommerce-prod
   ```

3. **Rollback to specific revision**:
   ```bash
   kubectl rollout undo deployment/api-gateway --to-revision=2 -n ecommerce-prod
   ```

## Troubleshooting

### Common Issues

1. **Pod not starting**:
   ```bash
   kubectl describe pod <pod-name> -n ecommerce-prod
   kubectl logs <pod-name> -n ecommerce-prod
   ```

2. **Service not accessible**:
   ```bash
   kubectl get endpoints -n ecommerce-prod
   kubectl describe service <service-name> -n ecommerce-prod
   ```

3. **Database connection issues**:
   ```bash
   kubectl exec -it <pod-name> -n ecommerce-prod -- env | grep DATABASE
   ```

### Performance Issues

1. **Check resource usage**:
   ```bash
   kubectl top pods -n ecommerce-prod
   kubectl top nodes
   ```

2. **Check metrics**:
   ```bash
   # Access Grafana dashboard
   kubectl port-forward service/grafana 3000:3000 -n monitoring
   ```

## Security Considerations

### Network Policies

Implement network policies to restrict traffic:

```yaml
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: api-gateway-netpol
spec:
  podSelector:
    matchLabels:
      app: api-gateway
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          name: ingress-nginx
  egress:
  - to:
    - podSelector:
        matchLabels:
          app: user-service
```

### Pod Security

Use security contexts and policies:

```yaml
securityContext:
  runAsNonRoot: true
  runAsUser: 1001
  allowPrivilegeEscalation: false
  readOnlyRootFilesystem: true
  capabilities:
    drop:
    - ALL
```

## Maintenance

### Regular Tasks

1. **Update dependencies**:
   ```bash
   npm audit
   npm update
   ```

2. **Update base images**:
   ```bash
   # Rebuild with latest base images
   docker build --no-cache -t ecommerce/api-gateway:latest .
   ```

3. **Certificate renewal**:
   ```bash
   # Check certificate expiry
   kubectl get certificates -n ecommerce-prod
   ```

### Scheduled Maintenance

Plan for regular maintenance windows:

1. **Database maintenance**
2. **Security updates**
3. **Performance optimization**
4. **Backup verification**
