import { Router } from 'express'
import { UserController } from '../controllers/userController.js'

const router = Router()
const userController = new UserController()

// Get user profile
router.get('/profile', userController.getProfile)

// Update user profile
router.put('/profile', userController.updateProfile)

// Delete user
router.delete('/profile', userController.deleteUser)

export { router as userRoutes }
