import { Router } from 'express'
import { ProductController } from '../controllers/productController.js'

const router = Router()
const productController = new ProductController()

// Get all products
router.get('/', productController.getAllProducts)

// Get product by ID
router.get('/:id', productController.getProductById)

// Create new product
router.post('/', productController.createProduct)

// Update product
router.put('/:id', productController.updateProduct)

// Delete product
router.delete('/:id', productController.deleteProduct)

export { router as productRoutes }
