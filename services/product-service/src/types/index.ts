export interface CreateProductRequest {
  name: string
  description: string
  price: number
  categoryId: string
  imageUrl?: string
  stock: number
}

export interface UpdateProductRequest {
  name?: string
  description?: string
  price?: number
  categoryId?: string
  imageUrl?: string
  stock?: number
  isActive?: boolean
}

export interface ProductFilters {
  categoryId?: string
  search?: string
  minPrice?: number
  maxPrice?: number
  inStock?: boolean
  page?: number
  limit?: number
}
