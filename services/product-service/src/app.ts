import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import morgan from 'morgan'
import compression from 'compression'
import dotenv from 'dotenv'
import { errorHandler } from './middleware/errorHandler.js'
import { logger } from './utils/logger.js'
import { healthRoutes } from './routes/health.js'
import { productRoutes } from './routes/products.js'
import { categoryRoutes } from './routes/categories.js'

dotenv.config()

const app = express()
const PORT = process.env.PRODUCT_SERVICE_PORT || 3002

// Security middleware
app.use(helmet())
app.use(cors())

// General middleware
app.use(compression())
app.use(morgan('combined', { stream: { write: (message) => logger.info(message.trim()) } }))
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true }))

// Routes
app.use('/health', healthRoutes)
app.use('/api/products', productRoutes)
app.use('/api/categories', categoryRoutes)

// Error handling
app.use(errorHandler)

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' })
})

app.listen(PORT, () => {
  logger.info(`Product Service running on port ${PORT}`)
})

export default app
