import { Request, Response, NextFunction } from 'express'
import { logger } from '../utils/logger.js'

export const errorHandler = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
) => {
  logger.error('Error occurred:', {
    error: error.message,
    stack: error.stack,
    url: req.url,
    method: req.method,
    ip: req.ip
  })

  // Default error
  let status = 500
  let message = 'Internal server error'

  // Handle specific error types
  if (error.name === 'ValidationError') {
    status = 400
    message = error.message
  } else if (error.name === 'UnauthorizedError') {
    status = 401
    message = 'Unauthorized'
  } else if (error.name === 'NotFoundError') {
    status = 404
    message = 'Resource not found'
  }

  res.status(status).json({
    success: false,
    error: message,
    ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
  })
}
