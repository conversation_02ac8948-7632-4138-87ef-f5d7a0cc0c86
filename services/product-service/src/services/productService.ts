import { Product } from '../models/Product.js'
import { logger } from '../utils/logger.js'

export class ProductService {
  async getAllProducts(filters?: {
    categoryId?: string
    search?: string
    page?: number
    limit?: number
  }) {
    try {
      const page = filters?.page || 1
      const limit = filters?.limit || 10
      
      const result = await Product.findAll(filters)
      
      return {
        products: result.products,
        pagination: {
          page,
          limit,
          total: result.total,
          totalPages: Math.ceil(result.total / limit)
        }
      }
    } catch (error) {
      logger.error('Error getting products:', error)
      throw error
    }
  }

  async getProductById(id: string): Promise<Product | null> {
    try {
      return await Product.findById(id)
    } catch (error) {
      logger.error('Error getting product by ID:', error)
      throw error
    }
  }

  async createProduct(productData: {
    name: string
    description: string
    price: number
    categoryId: string
    imageUrl?: string
    stock: number
  }): Promise<Product> {
    try {
      const product = new Product(productData)
      return await product.save()
    } catch (error) {
      logger.error('Error creating product:', error)
      throw error
    }
  }

  async updateProduct(id: string, updates: Partial<Product>): Promise<Product | null> {
    try {
      const product = await Product.findById(id)
      if (!product) {
        return null
      }

      Object.assign(product, updates)
      return await product.save()
    } catch (error) {
      logger.error('Error updating product:', error)
      throw error
    }
  }

  async deleteProduct(id: string): Promise<boolean> {
    try {
      const product = await Product.findById(id)
      if (!product) {
        return false
      }

      return await product.delete()
    } catch (error) {
      logger.error('Error deleting product:', error)
      throw error
    }
  }

  async updateStock(id: string, quantity: number): Promise<Product | null> {
    try {
      const product = await Product.findById(id)
      if (!product) {
        return null
      }

      return await product.updateStock(quantity)
    } catch (error) {
      logger.error('Error updating stock:', error)
      throw error
    }
  }
}
