{"name": "@ecommerce/product-service", "version": "1.0.0", "private": true, "type": "module", "scripts": {"dev": "tsx watch src/app.ts", "build": "tsc", "start": "node dist/app.js", "lint": "eslint . --ext .ts --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit", "test": "vitest", "test:coverage": "vitest --coverage", "clean": "rm -rf dist"}, "dependencies": {"express": "^5.0.0-beta.1", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "dotenv": "^16.3.0", "zod": "^3.22.0", "pg": "^8.11.0", "redis": "^4.6.0", "winston": "^3.11.0", "express-validator": "^7.0.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/pg": "^8.10.0", "@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "tsx": "^4.6.0", "typescript": "^5.3.0", "vitest": "^0.34.6", "supertest": "^6.3.3", "@types/supertest": "^2.0.16"}}