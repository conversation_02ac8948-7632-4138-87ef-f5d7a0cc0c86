{"extends": "../../tsconfig.json", "compilerOptions": {"target": "ES2022", "module": "ESNext", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "skipLibCheck": true, "strict": true, "noEmit": false, "outDir": "./dist", "rootDir": "./src", "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@shared/types": ["../../packages/shared-types/src"], "@shared/utils": ["../../packages/common-utils/src"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"], "references": [{"path": "../../packages/shared-types"}, {"path": "../../packages/common-utils"}]}