import { OrderStatus, PaymentStatus } from '@shared/types'

export interface CreateOrderRequest {
  items: Array<{
    productId: string
    quantity: number
  }>
  shippingAddress: {
    street: string
    city: string
    state: string
    zipCode: string
    country: string
  }
}

export interface UpdateOrderStatusRequest {
  status: OrderStatus
}

export interface OrderFilters {
  userId?: string
  status?: OrderStatus
  paymentStatus?: PaymentStatus
  startDate?: Date
  endDate?: Date
  page?: number
  limit?: number
}

export interface CartItem {
  productId: string
  quantity: number
  price: number
  total: number
}

export interface Cart {
  userId: string
  items: CartItem[]
  subtotal: number
}
