#!/bin/bash

set -e

echo "🏗️  Building E-commerce Microservices..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${G<PERSON><PERSON>}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

# Build shared packages first
print_status "Building shared packages..."
npm run build --workspace=packages/shared-types
npm run build --workspace=packages/ui-components
npm run build --workspace=packages/common-utils

# Build applications
print_status "Building applications..."
npm run build --workspace=apps/web
npm run build --workspace=apps/api-gateway
npm run build --workspace=apps/user-service
npm run build --workspace=apps/product-service
npm run build --workspace=apps/order-service

# Build Docker images
print_status "Building Docker images..."

SERVICES=("web" "api-gateway" "user-service" "product-service" "order-service")
REGISTRY=${DOCKER_REGISTRY:-"ecommerce"}
TAG=${DOCKER_TAG:-"latest"}

for service in "${SERVICES[@]}"; do
    print_status "Building Docker image for $service..."
    docker build -t "${REGISTRY}/${service}:${TAG}" -f "apps/${service}/Dockerfile" .
    
    if [ $? -eq 0 ]; then
        print_status "✅ Successfully built ${REGISTRY}/${service}:${TAG}"
    else
        print_error "❌ Failed to build ${REGISTRY}/${service}:${TAG}"
        exit 1
    fi
done

# Tag images as latest if not already
if [ "$TAG" != "latest" ]; then
    print_status "Tagging images as latest..."
    for service in "${SERVICES[@]}"; do
        docker tag "${REGISTRY}/${service}:${TAG}" "${REGISTRY}/${service}:latest"
    done
fi

print_status "🎉 Build completed successfully!"
print_status "Built images:"
for service in "${SERVICES[@]}"; do
    echo "  - ${REGISTRY}/${service}:${TAG}"
done

# Optional: Push to registry
if [ "$PUSH_TO_REGISTRY" = "true" ]; then
    print_status "Pushing images to registry..."
    for service in "${SERVICES[@]}"; do
        print_status "Pushing ${REGISTRY}/${service}:${TAG}..."
        docker push "${REGISTRY}/${service}:${TAG}"
        
        if [ "$TAG" != "latest" ]; then
            docker push "${REGISTRY}/${service}:latest"
        fi
    done
    print_status "✅ All images pushed to registry"
fi

print_status "Build script completed!"
