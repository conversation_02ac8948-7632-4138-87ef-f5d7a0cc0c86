#!/bin/bash

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[TEST]${NC} $1"
}

print_header "🧪 Running E-commerce Microservices Tests"

# Parse command line arguments
RUN_UNIT=true
RUN_INTEGRATION=false
RUN_E2E=false
COVERAGE=false
WATCH=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --integration)
            RUN_INTEGRATION=true
            shift
            ;;
        --e2e)
            RUN_E2E=true
            shift
            ;;
        --coverage)
            COVERAGE=true
            shift
            ;;
        --watch)
            WATCH=true
            shift
            ;;
        --all)
            RUN_UNIT=true
            RUN_INTEGRATION=true
            RUN_E2E=true
            shift
            ;;
        --help)
            echo "Usage: $0 [options]"
            echo "Options:"
            echo "  --integration    Run integration tests"
            echo "  --e2e           Run end-to-end tests"
            echo "  --coverage      Generate coverage reports"
            echo "  --watch         Run tests in watch mode"
            echo "  --all           Run all types of tests"
            echo "  --help          Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

# Check if Node.js is available
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed"
    exit 1
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    print_status "Installing dependencies..."
    npm ci
fi

# Run type checking first
print_status "Running type checks..."
npm run type-check

if [ $? -ne 0 ]; then
    print_error "Type checking failed"
    exit 1
fi

print_status "✅ Type checking passed"

# Run linting
print_status "Running linting..."
npm run lint

if [ $? -ne 0 ]; then
    print_error "Linting failed"
    exit 1
fi

print_status "✅ Linting passed"

# Prepare test command
TEST_CMD="npm run test"

if [ "$COVERAGE" = true ]; then
    TEST_CMD="npm run test:coverage"
fi

if [ "$WATCH" = true ]; then
    TEST_CMD="$TEST_CMD -- --watch"
fi

# Run unit tests
if [ "$RUN_UNIT" = true ]; then
    print_status "Running unit tests..."
    $TEST_CMD
    
    if [ $? -ne 0 ]; then
        print_error "Unit tests failed"
        exit 1
    fi
    
    print_status "✅ Unit tests passed"
fi

# Run integration tests
if [ "$RUN_INTEGRATION" = true ]; then
    print_status "Running integration tests..."
    
    # Start test database if needed
    if command -v docker-compose &> /dev/null; then
        print_status "Starting test database..."
        docker-compose -f docker-compose.test.yml up -d postgres redis
        
        # Wait for database to be ready
        sleep 5
    fi
    
    # Run integration tests
    npm run test:integration
    
    if [ $? -ne 0 ]; then
        print_error "Integration tests failed"
        
        # Cleanup
        if command -v docker-compose &> /dev/null; then
            docker-compose -f docker-compose.test.yml down
        fi
        
        exit 1
    fi
    
    print_status "✅ Integration tests passed"
    
    # Cleanup
    if command -v docker-compose &> /dev/null; then
        print_status "Cleaning up test database..."
        docker-compose -f docker-compose.test.yml down
    fi
fi

# Run E2E tests
if [ "$RUN_E2E" = true ]; then
    print_status "Running end-to-end tests..."
    
    # Start all services for E2E testing
    if command -v docker-compose &> /dev/null; then
        print_status "Starting all services for E2E testing..."
        docker-compose up -d
        
        # Wait for services to be ready
        print_status "Waiting for services to be ready..."
        sleep 30
        
        # Health check
        for i in {1..30}; do
            if curl -f http://localhost:3000/health > /dev/null 2>&1; then
                print_status "Services are ready"
                break
            fi
            
            if [ $i -eq 30 ]; then
                print_error "Services failed to start"
                docker-compose down
                exit 1
            fi
            
            sleep 2
        done
    fi
    
    # Run E2E tests
    npm run test:e2e
    
    if [ $? -ne 0 ]; then
        print_error "E2E tests failed"
        
        # Cleanup
        if command -v docker-compose &> /dev/null; then
            docker-compose down
        fi
        
        exit 1
    fi
    
    print_status "✅ E2E tests passed"
    
    # Cleanup
    if command -v docker-compose &> /dev/null; then
        print_status "Cleaning up services..."
        docker-compose down
    fi
fi

# Generate coverage report summary
if [ "$COVERAGE" = true ]; then
    print_status "Coverage reports generated:"
    find . -name "coverage" -type d | while read -r dir; do
        if [ -f "$dir/lcov-report/index.html" ]; then
            echo "  - $dir/lcov-report/index.html"
        fi
    done
fi

print_status "🎉 All tests completed successfully!"

# Summary
echo ""
print_header "Test Summary:"
if [ "$RUN_UNIT" = true ]; then
    echo "  ✅ Unit tests: PASSED"
fi
if [ "$RUN_INTEGRATION" = true ]; then
    echo "  ✅ Integration tests: PASSED"
fi
if [ "$RUN_E2E" = true ]; then
    echo "  ✅ E2E tests: PASSED"
fi
if [ "$COVERAGE" = true ]; then
    echo "  📊 Coverage reports: GENERATED"
fi

print_status "Test script completed!"
