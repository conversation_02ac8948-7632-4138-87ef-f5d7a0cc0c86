#!/bin/bash

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[SETUP]${NC} $1"
}

print_header "🚀 Setting up E-commerce Microservices Local Development Environment"

# Check prerequisites
print_status "Checking prerequisites..."

# Check Node.js
if ! command -v node &> /dev/null; then
    print_error "Node.js is not installed. Please install Node.js 18+ and try again."
    exit 1
fi

NODE_VERSION=$(node --version | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 18 ]; then
    print_error "Node.js version 18+ is required. Current version: $(node --version)"
    exit 1
fi

print_status "✅ Node.js $(node --version) is installed"

# Check npm
if ! command -v npm &> /dev/null; then
    print_error "npm is not installed"
    exit 1
fi

print_status "✅ npm $(npm --version) is installed"

# Check Docker
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker and try again."
    exit 1
fi

if ! docker info > /dev/null 2>&1; then
    print_error "Docker is not running. Please start Docker and try again."
    exit 1
fi

print_status "✅ Docker is installed and running"

# Check Docker Compose
if ! command -v docker-compose &> /dev/null; then
    print_warning "docker-compose is not installed. Trying docker compose..."
    if ! docker compose version > /dev/null 2>&1; then
        print_error "Neither docker-compose nor 'docker compose' is available"
        exit 1
    else
        DOCKER_COMPOSE_CMD="docker compose"
    fi
else
    DOCKER_COMPOSE_CMD="docker-compose"
fi

print_status "✅ Docker Compose is available"

# Install dependencies
print_status "Installing dependencies..."
npm ci

if [ $? -ne 0 ]; then
    print_error "Failed to install dependencies"
    exit 1
fi

print_status "✅ Dependencies installed"

# Build shared packages
print_status "Building shared packages..."
npm run build --workspace=packages/shared-types
npm run build --workspace=packages/ui-components  
npm run build --workspace=packages/common-utils

if [ $? -ne 0 ]; then
    print_error "Failed to build shared packages"
    exit 1
fi

print_status "✅ Shared packages built"

# Setup environment variables
print_status "Setting up environment variables..."

if [ ! -f ".env" ]; then
    if [ -f ".env.example" ]; then
        cp .env.example .env
        print_status "✅ Created .env file from .env.example"
        print_warning "Please review and update the .env file with your specific configuration"
    else
        print_warning ".env.example not found. You may need to create a .env file manually"
    fi
else
    print_status "✅ .env file already exists"
fi

# Create logs directory
print_status "Creating logs directory..."
mkdir -p logs
print_status "✅ Logs directory created"

# Start infrastructure services
print_status "Starting infrastructure services (PostgreSQL, Redis)..."

$DOCKER_COMPOSE_CMD up -d postgres redis

if [ $? -ne 0 ]; then
    print_error "Failed to start infrastructure services"
    exit 1
fi

print_status "✅ Infrastructure services started"

# Wait for services to be ready
print_status "Waiting for services to be ready..."

# Wait for PostgreSQL
for i in {1..30}; do
    if $DOCKER_COMPOSE_CMD exec -T postgres pg_isready -U postgres > /dev/null 2>&1; then
        print_status "✅ PostgreSQL is ready"
        break
    fi
    
    if [ $i -eq 30 ]; then
        print_error "PostgreSQL failed to start"
        exit 1
    fi
    
    sleep 2
done

# Wait for Redis
for i in {1..30}; do
    if $DOCKER_COMPOSE_CMD exec -T redis redis-cli ping > /dev/null 2>&1; then
        print_status "✅ Redis is ready"
        break
    fi
    
    if [ $i -eq 30 ]; then
        print_error "Redis failed to start"
        exit 1
    fi
    
    sleep 2
done

# Run database migrations (if migration scripts exist)
if [ -f "scripts/migrate.sh" ]; then
    print_status "Running database migrations..."
    ./scripts/migrate.sh
fi

# Setup Git hooks (if .git directory exists)
if [ -d ".git" ]; then
    print_status "Setting up Git hooks..."
    
    # Create pre-commit hook
    cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
echo "Running pre-commit checks..."
npm run lint
npm run type-check
EOF
    
    chmod +x .git/hooks/pre-commit
    print_status "✅ Git hooks configured"
fi

print_status "🎉 Local development environment setup completed!"

echo ""
print_header "Next Steps:"
echo "1. Review and update the .env file if needed"
echo "2. Start the development servers:"
echo "   npm run dev"
echo ""
echo "3. Access the applications:"
echo "   - Frontend: http://localhost:5173"
echo "   - API Gateway: http://localhost:3000"
echo "   - PostgreSQL: localhost:5432"
echo "   - Redis: localhost:6379"
echo ""
echo "4. To stop infrastructure services:"
echo "   $DOCKER_COMPOSE_CMD down"
echo ""

print_status "Happy coding! 🚀"
