#!/bin/bash

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${G<PERSON><PERSON>}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[DEPLOY]${NC} $1"
}

# Check if environment is provided
if [ -z "$1" ]; then
    print_error "Usage: $0 <environment>"
    print_error "Available environments: dev, staging, prod"
    exit 1
fi

ENVIRONMENT=$1
VALID_ENVS=("dev" "staging" "prod")

# Validate environment
if [[ ! " ${VALID_ENVS[@]} " =~ " ${ENVIRONMENT} " ]]; then
    print_error "Invalid environment: $ENVIRONMENT"
    print_error "Valid environments: ${VALID_ENVS[*]}"
    exit 1
fi

print_header "🚀 Deploying E-commerce Microservices to $ENVIRONMENT"

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    print_error "kubectl is not installed or not in PATH"
    exit 1
fi

# Check if kubectl is configured
if ! kubectl cluster-info &> /dev/null; then
    print_error "kubectl is not configured or cluster is not accessible"
    exit 1
fi

# Set namespace based on environment
NAMESPACE="ecommerce-${ENVIRONMENT}"

print_status "Using namespace: $NAMESPACE"

# Create namespace if it doesn't exist
if ! kubectl get namespace "$NAMESPACE" &> /dev/null; then
    print_status "Creating namespace: $NAMESPACE"
    kubectl create namespace "$NAMESPACE"
else
    print_status "Namespace $NAMESPACE already exists"
fi

# Apply Kubernetes manifests
print_status "Applying Kubernetes manifests for $ENVIRONMENT..."

if [ ! -d "infrastructure/k8s/environments/$ENVIRONMENT" ]; then
    print_error "Environment directory not found: infrastructure/k8s/environments/$ENVIRONMENT"
    exit 1
fi

# Apply using Kustomize
kubectl apply -k "infrastructure/k8s/environments/$ENVIRONMENT"

if [ $? -ne 0 ]; then
    print_error "Failed to apply Kubernetes manifests"
    exit 1
fi

print_status "✅ Kubernetes manifests applied successfully"

# Wait for deployments to be ready
print_status "Waiting for deployments to be ready..."

SERVICES=("api-gateway" "user-service" "product-service" "order-service")

for service in "${SERVICES[@]}"; do
    print_status "Waiting for $service deployment..."
    kubectl rollout status deployment/"$service" -n "$NAMESPACE" --timeout=300s
    
    if [ $? -eq 0 ]; then
        print_status "✅ $service deployment is ready"
    else
        print_error "❌ $service deployment failed or timed out"
        exit 1
    fi
done

# Check pod status
print_status "Checking pod status..."
kubectl get pods -n "$NAMESPACE"

# Get service endpoints
print_status "Service endpoints:"
kubectl get services -n "$NAMESPACE"

# Get ingress (if exists)
if kubectl get ingress -n "$NAMESPACE" &> /dev/null; then
    print_status "Ingress configuration:"
    kubectl get ingress -n "$NAMESPACE"
fi

print_status "🎉 Deployment to $ENVIRONMENT completed successfully!"

# Environment-specific post-deployment actions
case $ENVIRONMENT in
    "dev")
        print_status "Development environment deployed"
        print_status "You can access the application at: http://dev.ecommerce.local"
        ;;
    "staging")
        print_status "Staging environment deployed"
        print_status "You can access the application at: https://staging.ecommerce.com"
        ;;
    "prod")
        print_status "Production environment deployed"
        print_status "You can access the application at: https://ecommerce.com"
        print_warning "Production deployment completed. Monitor the application closely."
        ;;
esac

print_status "Deployment script completed!"
