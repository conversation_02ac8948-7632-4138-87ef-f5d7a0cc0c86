{"name": "ecommerce-microservices", "version": "1.0.0", "description": "A modern e-commerce platform built with microservices architecture", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo run dev", "build": "turbo run build", "test": "turbo run test", "lint": "turbo run lint", "type-check": "turbo run type-check", "clean": "turbo run clean", "format": "prettier --write \"**/*.{ts,tsx,js,jsx,json,md}\"", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,json,md}\"", "docker:build": "./scripts/build.sh", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "k8s:deploy": "./scripts/deploy.sh", "setup:local": "./scripts/setup-local.sh"}, "devDependencies": {"@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-import": "^2.29.0", "prettier": "^3.1.0", "turbo": "^1.11.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/your-username/ecommerce-microservices.git"}, "keywords": ["ecommerce", "microservices", "react", "nodejs", "typescript", "kubernetes", "docker"], "author": "Your Name", "license": "MIT"}