apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: ecommerce-staging

resources:
- ../../base/api-gateway
- ../../base/user-service
- ../../base/product-service
- ../../base/order-service

patchesStrategicMerge:
- patches/api-gateway-patch.yaml
- patches/user-service-patch.yaml
- patches/product-service-patch.yaml
- patches/order-service-patch.yaml

images:
- name: ecommerce/api-gateway
  newTag: staging
- name: ecommerce/user-service
  newTag: staging
- name: ecommerce/product-service
  newTag: staging
- name: ecommerce/order-service
  newTag: staging

configMapGenerator:
- name: app-config
  literals:
  - NODE_ENV=staging
  - LOG_LEVEL=info

secretGenerator:
- name: database-secret
  literals:
  - url=********************************************/ecommerce_staging
- name: redis-secret
  literals:
  - url=redis://redis:6379/1
- name: jwt-secret
  literals:
  - secret=staging-jwt-secret-change-in-production

commonLabels:
  environment: staging
  version: staging

commonAnnotations:
  deployment.kubernetes.io/revision: "1"
