apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: ecommerce-dev

resources:
- ../../base/api-gateway
- ../../base/user-service
- ../../base/product-service
- ../../base/order-service

patchesStrategicMerge:
- patches/api-gateway-patch.yaml
- patches/user-service-patch.yaml
- patches/product-service-patch.yaml
- patches/order-service-patch.yaml

images:
- name: ecommerce/api-gateway
  newTag: dev
- name: ecommerce/user-service
  newTag: dev
- name: ecommerce/product-service
  newTag: dev
- name: ecommerce/order-service
  newTag: dev

configMapGenerator:
- name: app-config
  literals:
  - NODE_ENV=development
  - LOG_LEVEL=debug
  - CORS_ORIGIN=http://localhost:5173

secretGenerator:
- name: database-secret
  literals:
  - url=********************************************/ecommerce_dev
- name: redis-secret
  literals:
  - url=redis://redis:6379/0
- name: jwt-secret
  literals:
  - secret=dev-jwt-secret-change-in-production

commonLabels:
  environment: development
  version: dev

commonAnnotations:
  deployment.kubernetes.io/revision: "1"
