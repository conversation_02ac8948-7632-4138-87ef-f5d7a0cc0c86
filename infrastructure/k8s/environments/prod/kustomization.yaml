apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: ecommerce-prod

resources:
- ../../base/api-gateway
- ../../base/user-service
- ../../base/product-service
- ../../base/order-service

patchesStrategicMerge:
- patches/api-gateway-patch.yaml
- patches/user-service-patch.yaml
- patches/product-service-patch.yaml
- patches/order-service-patch.yaml

images:
- name: ecommerce/api-gateway
  newTag: latest
- name: ecommerce/user-service
  newTag: latest
- name: ecommerce/product-service
  newTag: latest
- name: ecommerce/order-service
  newTag: latest

configMapGenerator:
- name: app-config
  literals:
  - NODE_ENV=production
  - LOG_LEVEL=warn

# Secrets should be created manually in production
# secretGenerator:
# - name: database-secret
#   literals:
#   - url=***********************************/ecommerce_prod

commonLabels:
  environment: production
  version: latest

commonAnnotations:
  deployment.kubernetes.io/revision: "1"
