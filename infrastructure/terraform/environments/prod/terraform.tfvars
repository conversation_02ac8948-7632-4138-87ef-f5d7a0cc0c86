project_name = "ecommerce"
environment  = "prod"
aws_region   = "us-west-2"

# VPC Configuration
vpc_cidr = "10.0.0.0/16"

# EKS Configuration
kubernetes_version = "1.28"
node_groups = {
  main = {
    instance_types = ["t3.medium"]
    min_size       = 2
    max_size       = 10
    desired_size   = 3
  }
  spot = {
    instance_types = ["t3.medium", "t3.large"]
    min_size       = 0
    max_size       = 5
    desired_size   = 2
  }
}

# Database Configuration
postgres_version   = "15.4"
db_instance_class  = "db.t3.small"
database_name      = "ecommerce_prod"
database_username  = "ecommerce_user"

# Redis Configuration
redis_node_type  = "cache.t3.small"
redis_num_nodes  = 2

# Tags
common_tags = {
  Project     = "ecommerce"
  Environment = "production"
  ManagedBy   = "terraform"
  Owner       = "platform-team"
  CostCenter  = "engineering"
}
