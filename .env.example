# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/ecommerce
REDIS_URL=redis://localhost:6379

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# API Gateway Configuration
API_GATEWAY_PORT=3000
API_GATEWAY_HOST=localhost

# Service Ports
USER_SERVICE_PORT=3001
USER_SERVICE_HOST=localhost

PRODUCT_SERVICE_PORT=3002
PRODUCT_SERVICE_HOST=localhost

ORDER_SERVICE_PORT=3003
ORDER_SERVICE_HOST=localhost

# Frontend Configuration
VITE_API_URL=http://localhost:3000
VITE_APP_NAME=E-commerce Platform

# External Services
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key

EMAIL_SERVICE_API_KEY=your_email_service_api_key
EMAIL_FROM=<EMAIL>

# AWS Configuration (for production)
AWS_REGION=us-west-2
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
S3_BUCKET_NAME=ecommerce-assets

# Monitoring
PROMETHEUS_PORT=9090
GRAFANA_PORT=3001
JAEGER_PORT=16686

# Environment
NODE_ENV=development
LOG_LEVEL=debug

# CORS Configuration
CORS_ORIGIN=http://localhost:5173

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
