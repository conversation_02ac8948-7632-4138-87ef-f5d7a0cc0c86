import { Router } from 'express'
import { OrderController } from '../controllers/orderController.js'

const router = Router()
const orderController = new OrderController()

// Get all orders
router.get('/', orderController.getAllOrders)

// Get order by ID
router.get('/:id', orderController.getOrderById)

// Create new order
router.post('/', orderController.createOrder)

// Update order status
router.patch('/:id/status', orderController.updateOrderStatus)

// Cancel order
router.patch('/:id/cancel', orderController.cancelOrder)

export { router as orderRoutes }
