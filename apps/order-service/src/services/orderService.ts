import { Order } from '../models/Order.js'
import { OrderStatus, PaymentStatus } from '@shared/types'
import { logger } from '../utils/logger.js'

export class OrderService {
  async getAllOrders(filters?: {
    userId?: string
    status?: OrderStatus
    page?: number
    limit?: number
  }) {
    try {
      const page = filters?.page || 1
      const limit = filters?.limit || 10
      
      const result = await Order.findAll(filters)
      
      return {
        orders: result.orders,
        pagination: {
          page,
          limit,
          total: result.total,
          totalPages: Math.ceil(result.total / limit)
        }
      }
    } catch (error) {
      logger.error('Error getting orders:', error)
      throw error
    }
  }

  async getOrderById(id: string): Promise<Order | null> {
    try {
      return await Order.findById(id)
    } catch (error) {
      logger.error('Error getting order by ID:', error)
      throw error
    }
  }

  async createOrder(orderData: {
    userId: string
    items: Array<{
      productId: string
      quantity: number
      price: number
    }>
    shippingAddress: any
  }): Promise<Order> {
    try {
      const order = new Order({
        userId: orderData.userId,
        items: orderData.items.map(item => ({
          id: '',
          orderId: '',
          productId: item.productId,
          quantity: item.quantity,
          price: item.price,
          total: item.quantity * item.price
        })),
        shippingAddress: orderData.shippingAddress,
        status: 'pending',
        paymentStatus: 'pending'
      })

      order.calculateTotal()
      return await order.save()
    } catch (error) {
      logger.error('Error creating order:', error)
      throw error
    }
  }

  async updateOrderStatus(id: string, status: OrderStatus): Promise<Order | null> {
    try {
      const order = await Order.findById(id)
      if (!order) {
        return null
      }

      return await order.updateStatus(status)
    } catch (error) {
      logger.error('Error updating order status:', error)
      throw error
    }
  }

  async updatePaymentStatus(id: string, paymentStatus: PaymentStatus): Promise<Order | null> {
    try {
      const order = await Order.findById(id)
      if (!order) {
        return null
      }

      return await order.updatePaymentStatus(paymentStatus)
    } catch (error) {
      logger.error('Error updating payment status:', error)
      throw error
    }
  }

  async getUserOrders(userId: string): Promise<Order[]> {
    try {
      return await Order.findByUserId(userId)
    } catch (error) {
      logger.error('Error getting user orders:', error)
      throw error
    }
  }
}
