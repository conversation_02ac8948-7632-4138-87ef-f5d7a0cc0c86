import { User } from '../models/User.js'
import { logger } from '../utils/logger.js'

export class UserService {
  async createUser(userData: {
    email: string
    password: string
    firstName: string
    lastName: string
  }): Promise<User> {
    try {
      // TODO: Hash password
      // TODO: Check if user already exists
      // TODO: Create user in database
      
      logger.info('Creating new user', { email: userData.email })
      
      const user = new User({
        email: userData.email,
        firstName: userData.firstName,
        lastName: userData.lastName,
        role: 'customer'
      })
      
      return await user.save()
    } catch (error) {
      logger.error('Error creating user:', error)
      throw error
    }
  }

  async getUserById(id: string): Promise<User | null> {
    try {
      return await User.findById(id)
    } catch (error) {
      logger.error('Error getting user by ID:', error)
      throw error
    }
  }

  async getUserByEmail(email: string): Promise<User | null> {
    try {
      return await User.findByEmail(email)
    } catch (error) {
      logger.error('Error getting user by email:', error)
      throw error
    }
  }

  async updateUser(id: string, updates: Partial<User>): Promise<User | null> {
    try {
      const user = await User.findById(id)
      if (!user) {
        return null
      }

      Object.assign(user, updates)
      return await user.save()
    } catch (error) {
      logger.error('Error updating user:', error)
      throw error
    }
  }

  async deleteUser(id: string): Promise<boolean> {
    try {
      const user = await User.findById(id)
      if (!user) {
        return false
      }

      return await user.delete()
    } catch (error) {
      logger.error('Error deleting user:', error)
      throw error
    }
  }
}
