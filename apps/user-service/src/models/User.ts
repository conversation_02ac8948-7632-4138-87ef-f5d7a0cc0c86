import { User as UserType } from '@shared/types'

export class User {
  id: string
  email: string
  firstName: string
  lastName: string
  role: 'customer' | 'admin'
  passwordHash: string
  createdAt: Date
  updatedAt: Date

  constructor(data: Partial<UserType> & { passwordHash?: string }) {
    this.id = data.id || ''
    this.email = data.email || ''
    this.firstName = data.firstName || ''
    this.lastName = data.lastName || ''
    this.role = data.role || 'customer'
    this.passwordHash = data.passwordHash || ''
    this.createdAt = data.createdAt || new Date()
    this.updatedAt = data.updatedAt || new Date()
  }

  toJSON(): Omit<User, 'passwordHash'> {
    const { passwordHash, ...user } = this
    return user
  }

  static async findById(id: string): Promise<User | null> {
    // TODO: Implement database query
    return null
  }

  static async findByEmail(email: string): Promise<User | null> {
    // TODO: Implement database query
    return null
  }

  async save(): Promise<User> {
    // TODO: Implement database save
    this.updatedAt = new Date()
    return this
  }

  async delete(): Promise<boolean> {
    // TODO: Implement database delete
    return true
  }
}
