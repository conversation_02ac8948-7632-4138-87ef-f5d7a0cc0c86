import { Router } from 'express'

const router = Router()

// Get all categories
router.get('/', (req, res) => {
  res.json({
    success: true,
    data: [],
    message: 'Get categories - implementation needed'
  })
})

// Get category by ID
router.get('/:id', (req, res) => {
  const { id } = req.params
  res.json({
    success: true,
    message: `Get category ${id} - implementation needed`
  })
})

// Create new category
router.post('/', (req, res) => {
  res.status(201).json({
    success: true,
    message: 'Create category - implementation needed'
  })
})

// Update category
router.put('/:id', (req, res) => {
  const { id } = req.params
  res.json({
    success: true,
    message: `Update category ${id} - implementation needed`
  })
})

// Delete category
router.delete('/:id', (req, res) => {
  const { id } = req.params
  res.json({
    success: true,
    message: `Delete category ${id} - implementation needed`
  })
})

export { router as categoryRoutes }
