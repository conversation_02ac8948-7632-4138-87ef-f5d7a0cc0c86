import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import morgan from 'morgan'
import compression from 'compression'
import rateLimit from 'express-rate-limit'
import { createProxyMiddleware } from 'http-proxy-middleware'
import dotenv from 'dotenv'
import { authMiddleware } from './middleware/auth.js'
import { errorHandler } from './middleware/errorHandler.js'
import { logger } from './utils/logger.js'
import { healthRoutes } from './routes/health.js'
import { authRoutes } from './routes/auth.js'

dotenv.config()

const app = express()
const PORT = process.env.API_GATEWAY_PORT || 3000

// Security middleware
app.use(helmet())
app.use(cors({
  origin: process.env.CORS_ORIGIN || 'http://localhost:5173',
  credentials: true
}))

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000'), // 15 minutes
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
  message: 'Too many requests from this IP, please try again later.'
})
app.use(limiter)

// General middleware
app.use(compression())
app.use(morgan('combined', { stream: { write: (message) => logger.info(message.trim()) } }))
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true }))

// Health check routes
app.use('/health', healthRoutes)

// Auth routes (handled by gateway)
app.use('/api/auth', authRoutes)

// Service proxies
const serviceProxyOptions = {
  changeOrigin: true,
  pathRewrite: {
    '^/api/users': '/api',
    '^/api/products': '/api',
    '^/api/orders': '/api'
  },
  onError: (err: Error, req: express.Request, res: express.Response) => {
    logger.error('Proxy error:', err)
    res.status(503).json({ error: 'Service temporarily unavailable' })
  }
}

// User service proxy
app.use('/api/users', 
  authMiddleware,
  createProxyMiddleware({
    target: `http://${process.env.USER_SERVICE_HOST || 'localhost'}:${process.env.USER_SERVICE_PORT || 3001}`,
    ...serviceProxyOptions
  })
)

// Product service proxy
app.use('/api/products',
  createProxyMiddleware({
    target: `http://${process.env.PRODUCT_SERVICE_HOST || 'localhost'}:${process.env.PRODUCT_SERVICE_PORT || 3002}`,
    ...serviceProxyOptions
  })
)

// Order service proxy
app.use('/api/orders',
  authMiddleware,
  createProxyMiddleware({
    target: `http://${process.env.ORDER_SERVICE_HOST || 'localhost'}:${process.env.ORDER_SERVICE_PORT || 3003}`,
    ...serviceProxyOptions
  })
)

// Error handling
app.use(errorHandler)

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' })
})

app.listen(PORT, () => {
  logger.info(`API Gateway running on port ${PORT}`)
})

export default app
