import { useParams } from 'react-router-dom'

export function ProductDetailPage() {
  const { id } = useParams()

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div>
          <div className="aspect-w-1 aspect-h-1 bg-gray-200 rounded-lg">
            {/* Product image */}
          </div>
        </div>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Product {id}</h1>
          <p className="text-2xl font-bold text-indigo-600 mt-4">$99.99</p>
          <p className="text-gray-600 mt-4">
            Product description goes here. This is a detailed description of the product.
          </p>
          <button className="mt-6 bg-indigo-600 text-white px-6 py-3 rounded-md hover:bg-indigo-700">
            Add to Cart
          </button>
        </div>
      </div>
    </div>
  )
}
