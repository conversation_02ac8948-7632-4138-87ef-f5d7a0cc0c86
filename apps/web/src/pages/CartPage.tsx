export function CartPage() {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">Shopping Cart</h1>
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          {/* Cart items */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <p className="text-gray-500">Your cart is empty</p>
          </div>
        </div>
        <div>
          {/* Order summary */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h2>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span>Subtotal</span>
                <span>$0.00</span>
              </div>
              <div className="flex justify-between">
                <span>Shipping</span>
                <span>$0.00</span>
              </div>
              <div className="flex justify-between font-semibold">
                <span>Total</span>
                <span>$0.00</span>
              </div>
            </div>
            <button className="w-full mt-4 bg-indigo-600 text-white py-3 rounded-md hover:bg-indigo-700">
              Proceed to Checkout
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
