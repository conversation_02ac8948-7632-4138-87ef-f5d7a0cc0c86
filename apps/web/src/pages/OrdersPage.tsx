export function OrdersPage() {
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">Order History</h1>
      <div className="space-y-6">
        {/* Sample order */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex justify-between items-start mb-4">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Order #12345</h3>
              <p className="text-gray-600">Placed on January 15, 2024</p>
            </div>
            <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-sm">
              Delivered
            </span>
          </div>
          <div className="border-t pt-4">
            <div className="flex justify-between items-center">
              <div>
                <p className="font-medium">2 items</p>
                <p className="text-gray-600">Total: $199.98</p>
              </div>
              <div className="space-x-2">
                <button className="text-indigo-600 hover:text-indigo-700">
                  View Details
                </button>
                <button className="text-indigo-600 hover:text-indigo-700">
                  Reorder
                </button>
              </div>
            </div>
          </div>
        </div>
        
        {/* Empty state */}
        <div className="text-center py-12">
          <p className="text-gray-500">No orders found</p>
        </div>
      </div>
    </div>
  )
}
