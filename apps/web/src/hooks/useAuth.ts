import { useState, useEffect } from 'react'
import { User } from '@shared/types'

interface AuthState {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
}

export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isLoading: true,
    isAuthenticated: false,
  })

  useEffect(() => {
    // Check for stored auth token and validate
    const token = localStorage.getItem('authToken')
    if (token) {
      // TODO: Validate token and get user info
      setAuthState({
        user: null, // TODO: Set actual user data
        isLoading: false,
        isAuthenticated: false, // TODO: Set based on token validation
      })
    } else {
      setAuthState({
        user: null,
        isLoading: false,
        isAuthenticated: false,
      })
    }
  }, [])

  const login = async (email: string, password: string) => {
    // TODO: Implement login logic
    console.log('Login:', { email, password })
  }

  const logout = () => {
    localStorage.removeItem('authToken')
    setAuthState({
      user: null,
      isLoading: false,
      isAuthenticated: false,
    })
  }

  const register = async (userData: {
    email: string
    password: string
    firstName: string
    lastName: string
  }) => {
    // TODO: Implement registration logic
    console.log('Register:', userData)
  }

  return {
    ...authState,
    login,
    logout,
    register,
  }
}
