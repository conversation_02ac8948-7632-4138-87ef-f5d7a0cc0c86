// Re-export shared types
export * from '@shared/types'

// Frontend-specific types
export interface AuthContextType {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  login: (email: string, password: string) => Promise<void>
  logout: () => void
  register: (userData: RegisterData) => Promise<void>
}

export interface RegisterData {
  email: string
  password: string
  firstName: string
  lastName: string
}

export interface CartContextType {
  items: CartItem[]
  total: number
  addItem: (productId: string, quantity: number) => void
  updateItem: (itemId: string, quantity: number) => void
  removeItem: (itemId: string) => void
  clearCart: () => void
}

export interface ProductFilters {
  search?: string
  category?: string
  minPrice?: number
  maxPrice?: number
  sortBy?: 'name' | 'price' | 'created'
  sortOrder?: 'asc' | 'desc'
}

// Import User type from shared types
import { User, CartItem } from '@shared/types'
