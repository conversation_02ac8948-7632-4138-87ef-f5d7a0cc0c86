{"extends": "../../tsconfig.json", "compilerOptions": {"target": "ES2020", "useDefineForClassFields": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "module": "ESNext", "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@/components/*": ["./src/components/*"], "@/hooks/*": ["./src/hooks/*"], "@/lib/*": ["./src/lib/*"], "@/pages/*": ["./src/pages/*"], "@/services/*": ["./src/services/*"], "@/types/*": ["./src/types/*"], "@/utils/*": ["./src/utils/*"], "@shared/types": ["../../packages/shared-types/src"], "@shared/ui": ["../../packages/ui-components/src"], "@shared/utils": ["../../packages/common-utils/src"]}}, "include": ["src"], "references": [{"path": "../../packages/shared-types"}, {"path": "../../packages/ui-components"}, {"path": "../../packages/common-utils"}]}