{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM", "DOM.Iterable"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "ESNext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "declaration": true, "declarationMap": true, "incremental": true, "composite": true, "baseUrl": ".", "paths": {"@shared/types": ["./packages/shared-types/src"], "@shared/ui": ["./packages/ui-components/src"], "@shared/utils": ["./packages/common-utils/src"]}}, "include": [], "exclude": ["node_modules", "dist", "build"], "references": [{"path": "./apps/web"}, {"path": "./apps/api-gateway"}, {"path": "./apps/user-service"}, {"path": "./apps/product-service"}, {"path": "./apps/order-service"}, {"path": "./packages/shared-types"}, {"path": "./packages/ui-components"}, {"path": "./packages/common-utils"}]}