name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    name: Test and Lint
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Type check
        run: npm run type-check

      - name: Lint
        run: npm run lint

      - name: Format check
        run: npm run format:check

      - name: Run tests
        run: npm run test

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        if: success()

  build:
    name: Build Applications
    runs-on: ubuntu-latest
    needs: test
    
    strategy:
      matrix:
        app: [web, api-gateway, user-service, product-service, order-service]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build ${{ matrix.app }}
        run: npm run build --workspace=apps/${{ matrix.app }}

      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: ${{ matrix.app }}-build
          path: apps/${{ matrix.app }}/dist

  docker-build:
    name: Build Docker Images
    runs-on: ubuntu-latest
    needs: [test, build]
    if: github.event_name == 'push'
    
    strategy:
      matrix:
        app: [web, api-gateway, user-service, product-service, order-service]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/${{ matrix.app }}
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: apps/${{ matrix.app }}/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

  deploy-dev:
    name: Deploy to Development
    runs-on: ubuntu-latest
    needs: docker-build
    if: github.ref == 'refs/heads/develop'
    environment: development
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure kubectl
        uses: azure/k8s-set-context@v3
        with:
          method: kubeconfig
          kubeconfig: ${{ secrets.KUBE_CONFIG_DEV }}

      - name: Deploy to Kubernetes
        run: |
          kubectl apply -k infrastructure/k8s/environments/dev
          kubectl rollout status deployment/api-gateway -n ecommerce-dev
          kubectl rollout status deployment/user-service -n ecommerce-dev
          kubectl rollout status deployment/product-service -n ecommerce-dev
          kubectl rollout status deployment/order-service -n ecommerce-dev

  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: docker-build
    if: github.ref == 'refs/heads/main'
    environment: staging
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure kubectl
        uses: azure/k8s-set-context@v3
        with:
          method: kubeconfig
          kubeconfig: ${{ secrets.KUBE_CONFIG_STAGING }}

      - name: Deploy to Kubernetes
        run: |
          kubectl apply -k infrastructure/k8s/environments/staging
          kubectl rollout status deployment/api-gateway -n ecommerce-staging
          kubectl rollout status deployment/user-service -n ecommerce-staging
          kubectl rollout status deployment/product-service -n ecommerce-staging
          kubectl rollout status deployment/order-service -n ecommerce-staging

  deploy-prod:
    name: Deploy to Production
    runs-on: ubuntu-latest
    needs: deploy-staging
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure kubectl
        uses: azure/k8s-set-context@v3
        with:
          method: kubeconfig
          kubeconfig: ${{ secrets.KUBE_CONFIG_PROD }}

      - name: Deploy to Kubernetes
        run: |
          kubectl apply -k infrastructure/k8s/environments/prod
          kubectl rollout status deployment/api-gateway -n ecommerce-prod
          kubectl rollout status deployment/user-service -n ecommerce-prod
          kubectl rollout status deployment/product-service -n ecommerce-prod
          kubectl rollout status deployment/order-service -n ecommerce-prod
