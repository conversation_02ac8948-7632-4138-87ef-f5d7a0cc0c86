import { z } from 'zod'

// User Domain Types
export const UserSchema = z.object({
  id: z.string(),
  email: z.string().email(),
  firstName: z.string(),
  lastName: z.string(),
  role: z.enum(['customer', 'admin']),
  createdAt: z.date(),
  updatedAt: z.date(),
})

// Product Domain Types
export const CategorySchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
})

export const ProductSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  price: z.number(),
  categoryId: z.string(),
  category: CategorySchema.optional(),
  imageUrl: z.string().optional(),
  stock: z.number(),
  isActive: z.boolean(),
  createdAt: z.date(),
  updatedAt: z.date(),
})

// Order Domain Types
export const AddressSchema = z.object({
  id: z.string().optional(),
  street: z.string(),
  city: z.string(),
  state: z.string(),
  zipCode: z.string(),
  country: z.string(),
})

export const OrderItemSchema = z.object({
  id: z.string(),
  orderId: z.string(),
  productId: z.string(),
  product: ProductSchema.optional(),
  quantity: z.number(),
  price: z.number(),
  total: z.number(),
})

export const OrderSchema = z.object({
  id: z.string(),
  userId: z.string(),
  user: UserSchema.optional(),
  items: z.array(OrderItemSchema),
  status: z.enum(['pending', 'confirmed', 'shipped', 'delivered', 'cancelled']),
  subtotal: z.number(),
  tax: z.number(),
  shipping: z.number(),
  total: z.number(),
  shippingAddress: AddressSchema,
  paymentMethod: z.string().optional(),
  paymentStatus: z.enum(['pending', 'paid', 'failed', 'refunded']),
  createdAt: z.date(),
  updatedAt: z.date(),
})

// Cart Domain Types
export const CartItemSchema = z.object({
  id: z.string(),
  cartId: z.string(),
  productId: z.string(),
  product: ProductSchema.optional(),
  quantity: z.number(),
  price: z.number(),
  total: z.number(),
})

export const CartSchema = z.object({
  id: z.string(),
  userId: z.string(),
  items: z.array(CartItemSchema),
  subtotal: z.number(),
  createdAt: z.date(),
  updatedAt: z.date(),
})

// Type exports
export type User = z.infer<typeof UserSchema>
export type Category = z.infer<typeof CategorySchema>
export type Product = z.infer<typeof ProductSchema>
export type Address = z.infer<typeof AddressSchema>
export type OrderItem = z.infer<typeof OrderItemSchema>
export type Order = z.infer<typeof OrderSchema>
export type CartItem = z.infer<typeof CartItemSchema>
export type Cart = z.infer<typeof CartSchema>

// Enums
export type UserRole = User['role']
export type OrderStatus = Order['status']
export type PaymentStatus = Order['paymentStatus']
