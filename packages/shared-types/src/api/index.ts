import { z } from 'zod'

// Common API Response Types
export const ApiResponseSchema = z.object({
  success: z.boolean(),
  message: z.string().optional(),
  data: z.any().optional(),
  error: z.string().optional(),
})

export const PaginationSchema = z.object({
  page: z.number().min(1),
  limit: z.number().min(1).max(100),
  total: z.number(),
  totalPages: z.number(),
})

export const PaginatedResponseSchema = z.object({
  success: z.boolean(),
  data: z.array(z.any()),
  pagination: PaginationSchema,
})

// Auth API Types
export const LoginRequestSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
})

export const RegisterRequestSchema = z.object({
  email: z.string().email(),
  password: z.string().min(6),
  firstName: z.string().min(1),
  lastName: z.string().min(1),
})

export const AuthResponseSchema = z.object({
  success: z.boolean(),
  data: z.object({
    user: z.object({
      id: z.string(),
      email: z.string(),
      firstName: z.string(),
      lastName: z.string(),
    }),
    token: z.string(),
  }),
})

// Product API Types
export const CreateProductRequestSchema = z.object({
  name: z.string().min(1),
  description: z.string(),
  price: z.number().positive(),
  categoryId: z.string(),
  imageUrl: z.string().url().optional(),
  stock: z.number().min(0),
})

export const UpdateProductRequestSchema = CreateProductRequestSchema.partial()

// Order API Types
export const CreateOrderRequestSchema = z.object({
  items: z.array(z.object({
    productId: z.string(),
    quantity: z.number().positive(),
  })),
  shippingAddress: z.object({
    street: z.string(),
    city: z.string(),
    state: z.string(),
    zipCode: z.string(),
    country: z.string(),
  }),
})

// Type exports
export type ApiResponse<T = any> = z.infer<typeof ApiResponseSchema> & { data?: T }
export type PaginatedResponse<T = any> = z.infer<typeof PaginatedResponseSchema> & { data: T[] }
export type Pagination = z.infer<typeof PaginationSchema>

export type LoginRequest = z.infer<typeof LoginRequestSchema>
export type RegisterRequest = z.infer<typeof RegisterRequestSchema>
export type AuthResponse = z.infer<typeof AuthResponseSchema>

export type CreateProductRequest = z.infer<typeof CreateProductRequestSchema>
export type UpdateProductRequest = z.infer<typeof UpdateProductRequestSchema>

export type CreateOrderRequest = z.infer<typeof CreateOrderRequestSchema>
