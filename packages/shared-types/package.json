{"name": "@ecommerce/shared-types", "version": "1.0.0", "private": true, "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint . --ext .ts --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"zod": "^3.22.0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "typescript": "^5.3.0"}}