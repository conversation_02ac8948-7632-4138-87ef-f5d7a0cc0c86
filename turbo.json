{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", "build/**", ".next/**", "!.next/cache/**"]}, "dev": {"cache": false, "persistent": true}, "test": {"dependsOn": ["build"], "outputs": ["coverage/**"], "inputs": ["src/**/*.tsx", "src/**/*.ts", "test/**/*.ts", "test/**/*.tsx"]}, "lint": {"dependsOn": ["^build"], "outputs": []}, "type-check": {"dependsOn": ["^build"], "outputs": []}, "clean": {"cache": false}}}